<?php
class ControllerAdminEmployee extends Controller {
	private $error = array();

	public function add() {
		$this->load->model('admin/employee');

		if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
			$this->model_admin_employee->addEmployee($this->request->post);

			$this->session->data['success'] = $this->language->get('text_add_success');

			$url = '';

			if (isset($this->request->get['filter_name'])) {
				$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_department'])) {
				$url .= '&filter_department=' . urlencode(html_entity_decode($this->request->get['filter_department'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_status'])) {
				$url .= '&filter_status=' . urlencode(html_entity_decode($this->request->get['filter_status'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_date_start'])) {
				$url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
			}

			if (isset($this->request->get['filter_date_end'])) {
				$url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
			}			

			if (isset($this->request->get['sort'])) {
				$url .= '&sort=' . $this->request->get['sort'];
			}

			if (isset($this->request->get['order'])) {
				$url .= '&order=' . $this->request->get['order'];
			}

			if (isset($this->request->get['page'])) {
				$url .= '&page=' . $this->request->get['page'];
			}

			$this->response->redirect($this->url->link('admin/employee/getList', 'token=' . $this->session->data['token'] . $url));
		}

		$this->getForm();
	}

	public function edit() {
		$this->load->model('admin/employee');

		if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
			$this->model_admin_employee->editEmployee($this->request->get['employee_id'], $this->request->post);

			$this->session->data['success'] = $this->language->get('text_edit_success');

			$url = '';

			if (isset($this->request->get['filter_name'])) {
				$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_department'])) {
				$url .= '&filter_department=' . urlencode(html_entity_decode($this->request->get['filter_department'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_status'])) {
				$url .= '&filter_status=' . urlencode(html_entity_decode($this->request->get['filter_status'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_date_start'])) {
				$url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
			}

			if (isset($this->request->get['filter_date_end'])) {
				$url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
			}

			if (isset($this->request->get['sort'])) {
				$url .= '&sort=' . $this->request->get['sort'];
			}

			if (isset($this->request->get['order'])) {
				$url .= '&order=' . $this->request->get['order'];
			}

			if (isset($this->request->get['page'])) {
				$url .= '&page=' . $this->request->get['page'];
			}

			$this->response->redirect($this->url->link('admin/employee/getList', 'token=' . $this->session->data['token'] . $url));
		}

		$this->getForm();
	}

	public function delete() {
		$this->load->model('admin/employee');

		if (isset($this->request->post['selected']) && $this->validateDelete()) {
			foreach ($this->request->post['selected'] as $employee_id) {
				$this->model_admin_employee->deleteEmployee($employee_id);
			}

			$this->session->data['success'] = $this->language->get('text_delete_success');

			$url = '';

			if (isset($this->request->get['filter_name'])) {
				$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_department'])) {
				$url .= '&filter_department=' . urlencode(html_entity_decode($this->request->get['filter_department'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_status'])) {
				$url .= '&filter_status=' . urlencode(html_entity_decode($this->request->get['filter_status'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_date_start'])) {
				$url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
			}

			if (isset($this->request->get['filter_date_end'])) {
				$url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
			}

			if (isset($this->request->get['sort'])) {
				$url .= '&sort=' . $this->request->get['sort'];
			}

			if (isset($this->request->get['order'])) {
				$url .= '&order=' . $this->request->get['order'];
			}

			if (isset($this->request->get['page'])) {
				$url .= '&page=' . $this->request->get['page'];
			}

			$this->response->redirect($this->url->link('admin/employee/getList', 'token=' . $this->session->data['token'] . $url));
		}

		$this->getList();
	}

	public function getRemind() {
		$this->load->model('admin/employee');
		$data['jobages'] = $this->model_admin_employee->getJobAgeRemind();
		$data['regulars'] = $this->model_admin_employee->getRegularRemind();
		$data['contracts'] = $this->model_admin_employee->getContractRemind();
		$data['insurances'] = $this->model_admin_employee->getInsuranceRemind();

		$data['action'] = $this->url->link('admin/employee/addLog', 'token=' . $this->session->data['token']);

		$data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('employee/remind.tpl', $data));
	}

	public function getList() {
		if (isset($this->request->get['filter_name'])) {
			$filter_name = $this->request->get['filter_name'];
		} else {
			$filter_name = '';
		}

		if (isset($this->request->get['filter_department'])) {
			$filter_department = $this->request->get['filter_department'];
		} else {
			$filter_department = '';
		}

		if (isset($this->request->get['filter_status'])) {
            $filter_status = $this->request->get['filter_status'];
        } else {
            $filter_status = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

		if (isset($this->request->get['sort'])) {
			$sort = $this->request->get['sort'];
		} else {
			$sort = 'job_work_date';
		}

		if (isset($this->request->get['order'])) {
			$order = $this->request->get['order'];
		} else {
			$order = 'DESC';
		}

		if (isset($this->request->get['page'])) {
			$page = $this->request->get['page'];
		} else {
			$page = 1;
		}

		$url = '';

		if (isset($this->request->get['filter_name'])) {
			$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_department'])) {
			$url .= '&filter_department=' . urlencode(html_entity_decode($this->request->get['filter_department'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_status'])) {
            $url .= '&filter_status=' . urlencode(html_entity_decode($this->request->get['filter_status'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

		if (isset($this->request->get['sort'])) {
			$url .= '&sort=' . $this->request->get['sort'];
		}

		if (isset($this->request->get['order'])) {
			$url .= '&order=' . $this->request->get['order'];
		}

		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}
		
		$data['add'] = $this->url->link('admin/employee/add', 'token=' . $this->session->data['token'] . $url);
		$data['delete'] = $this->url->link('admin/employee/delete', 'token=' . $this->session->data['token'] . $url);

		$data['employees'] = array();

		$filter_data = array(
			'filter_name'		=> $filter_name,
			'filter_department'	=> $filter_department,
			'filter_status'		=> $filter_status,
			'filter_date_start'	=> $filter_date_start,
			'filter_date_end'	=> $filter_date_end,
			'sort'			=> $sort,
			'order'			=> $order,
			'start'			=> ($page - 1) * $this->config->get('config_limit'),
			'limit'			=> $this->config->get('config_limit')
		);

		$this->load->model('admin/employee');
		$data['statuses'] = $this->model_admin_employee->getStatuses();
		$data['departments'] = $this->model_admin_employee->getDepartments();

		$results = $this->model_admin_employee->getEmployees($filter_data);

		foreach ($results as $result) {
			$data['employees'][] = array(
				'id'	=> $result['employee_id'],
				'name'	=> $result['fullname'],
				'phone' => $result['telephone'],
				'age'	=> $this->model_admin_employee->getAge('%s岁', $result['birth_date']),
				'job'   => $result['job_department'] . $result['job_post'],
				'jobage'=> $this->model_admin_employee->getAge('%s年%s个月', $result['job_work_date'], $result['job_leave_date']),
				'entry' => $result['job_work_date'],
				'status'=> $result['job_status'],
				'from'  => $result['channel_from'],
				'edit'  => $this->url->link('admin/employee/edit', 'token=' . $this->session->data['token'] . '&employee_id=' . $result['employee_id'] . $url),
                'view'	=> $this->url->link('admin/employee/getDetail', 'token=' . $this->session->data['token'] . '&employee_id=' . $result['employee_id'] . $url)
			);
		}

		$total = $this->model_admin_employee->getTotalEmployees($filter_data);

		if (isset($this->error['warning'])) {
			$data['warning'] = $this->error['warning'];
		} else {
			$data['warning'] = '';
		}

		if (isset($this->session->data['success'])) {
			$data['success'] = $this->session->data['success'];

			unset($this->session->data['success']);
		} else {
			$data['success'] = '';
		}

		$url = '';

		if (isset($this->request->get['filter_name'])) {
			$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_department'])) {
			$url .= '&filter_department=' . urlencode(html_entity_decode($this->request->get['filter_department'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_status'])) {
            $url .= '&filter_status=' . urlencode(html_entity_decode($this->request->get['filter_status'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

		if ($order == 'ASC') {
			$url .= '&order=DESC';
		} else {
			$url .= '&order=ASC';
		}

		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}

		$data['sort_name'] = $this->url->link('admin/employee/getList', 'token=' . $this->session->data['token'] . '&sort=fullname' . $url);
		$data['sort_phone'] = $this->url->link('admin/employee/getList', 'token=' . $this->session->data['token'] . '&sort=telephone' . $url);
		$data['sort_age'] = $this->url->link('admin/employee/getList', 'token=' . $this->session->data['token'] . '&sort=birth_date' . $url);
		$data['sort_job'] = $this->url->link('admin/employee/getList', 'token=' . $this->session->data['token'] . '&sort=job_post' . $url);
		$data['sort_jobage'] = $this->url->link('admin/employee/getList', 'token=' . $this->session->data['token'] . '&sort=job_work_date' . $url);
		$data['sort_status'] = $this->url->link('admin/employee/getList', 'token=' . $this->session->data['token'] . '&sort=job_status' . $url);
		$data['sort_entry'] = $this->url->link('admin/employee/getList', 'token=' . $this->session->data['token'] . '&sort=job_work_date' . $url);
		$data['sort_from'] = $this->url->link('admin/employee/getList', 'token=' . $this->session->data['token'] . '&sort=channel_from' . $url);

		$url = '';

		if (isset($this->request->get['filter_name'])) {
			$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_department'])) {
			$url .= '&filter_department=' . urlencode(html_entity_decode($this->request->get['filter_department'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_status'])) {
            $url .= '&filter_status=' . urlencode(html_entity_decode($this->request->get['filter_status'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

		if (isset($this->request->get['sort'])) {
			$url .= '&sort=' . $this->request->get['sort'];
		}

		if (isset($this->request->get['order'])) {
			$url .= '&order=' . $this->request->get['order'];
		}

		$pagination = new Pagination();
		$pagination->total = $total;
		$pagination->page = $page;
		$pagination->limit = $this->config->get('config_limit');
		$pagination->url = $this->url->link('admin/employee/getList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

		$pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

		$data['filter_name'] = $filter_name;
		$data['filter_department'] = $filter_department;
		$data['filter_status'] = $filter_status;
		$data['filter_date_start'] = $filter_date_start;
		$data['filter_date_end'] = $filter_date_end;

		$data['nofilter'] = $this->url->link('admin/employee/getList', 'token=' . $this->session->data['token']);

		$data['sort'] = $sort;
		$data['order'] = $order;

		$data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('employee/list.tpl', $data));
	}

    public function getDetail() {
        if (isset($this->request->get['employee_id'])) {
            $employee_id = $this->request->get['employee_id'];
        } else {
            $employee_id = 0;
        }
        
        $this->load->model('admin/employee');
        $data['employee_info'] = $this->model_admin_employee->getEmployee($employee_id);

        if (empty($data['employee_info'])) {
            $this->response->redirect($this->url->link('admin/employee/getList', 'token=' . $this->session->data['token']));
        }

        $data['contracts'] = $this->model_admin_employee->getContract($employee_id);
		$data['insurances'] = $this->model_admin_employee->getInsurance($employee_id);
		$data['logs'] = $this->model_admin_employee->getLogs($employee_id);

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('employee/detail.tpl', $data));
    }

    public function addLog() {
        if (isset($this->request->get['employee_id'])) {
            $employee_id = $this->request->get['employee_id'];
        } else {
            $employee_id = 0;
        }
        
        $this->load->model('admin/employee');
        $data['employee_info'] = $this->model_admin_employee->getEmployee($employee_id);

    	if (isset($this->request->get['action'])) {
            $action = $this->request->get['action'];
        } else {
            $action = '';
        }

        if (empty($data['employee_info']) || empty($action)) {
            $this->response->redirect($this->url->link('admin/employee/getRemind', 'token=' . $this->session->data['token']));
        }

        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
			$this->model_admin_employee->addLog($this->request->post);

			$this->response->redirect($this->url->link('admin/employee/getRemind', 'token=' . $this->session->data['token']));
		}

		$data['action'] = $this->url->link('admin/employee/addLog', 'token=' . $this->session->data['token'] . '&action=' . $action . '&employee_id=' . $employee_id);
		$data['cancel'] = $this->url->link('admin/employee/getRemind', 'token=' . $this->session->data['token']);

		if ($action == 'jobage') {
			$data['log_name'] = '员工司日';			
		} elseif ($action == 'regular') {
			$data['log_name'] = '员工转正';
		} elseif ($action == 'contract') {
			$data['log_name'] = '签订合同';
			$data['contracts'] = $this->model_admin_employee->getContract($employee_id);
		} elseif ($action == 'insurance') {
			$data['log_name'] = '保险增减';
			$data['insurances'] = $this->model_admin_employee->getInsurance($employee_id);
		}

		$data['log_action'] = $action;
		$data['statuses'] = $this->model_admin_employee->getStatuses();
		$data['companys'] = $this->model_admin_employee->getCompanys();
		$data['innames'] = $this->model_admin_employee->getInnames();
		
		$data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('employee/log.tpl', $data));
	}

	protected function getForm() {
		$data['text_form'] = !isset($this->request->get['employee_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

		if (isset($this->error['warning'])) {
			$data['warning'] = $this->error['warning'];
		} else {
			$data['warning'] = '';
		}

		$url = '';

		if (isset($this->request->get['filter_name'])) {
			$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_department'])) {
			$url .= '&filter_department=' . urlencode(html_entity_decode($this->request->get['filter_department'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_status'])) {
            $url .= '&filter_status=' . urlencode(html_entity_decode($this->request->get['filter_status'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

		if (isset($this->request->get['sort'])) {
			$url .= '&sort=' . $this->request->get['sort'];
		}

		if (isset($this->request->get['order'])) {
			$url .= '&order=' . $this->request->get['order'];
		}

		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}

		if (!isset($this->request->get['employee_id'])) {
			$data['action'] = $this->url->link('admin/employee/add', 'token=' . $this->session->data['token'] . $url);
		} else {
			$data['action'] = $this->url->link('admin/employee/edit', 'token=' . $this->session->data['token'] . '&employee_id=' . $this->request->get['employee_id'] . $url);
		}

		$data['cancel'] = $this->url->link('admin/employee/getList', 'token=' . $this->session->data['token'] . $url);
		$data['getToken'] = $this->url->link('admin/common/getUploadToken', 'token=' . $this->session->data['token']);

		if (isset($this->request->get['employee_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
			$employee_info = $this->model_admin_employee->getEmployee($this->request->get['employee_id']);
		}

		$data['statuses'] = $this->model_admin_employee->getStatuses();
		$data['departments'] = $this->model_admin_employee->getDepartments();
		$data['companys'] = $this->model_admin_employee->getCompanys();
		$data['offices'] = $this->model_admin_employee->getOffices();
		$data['degrees'] = $this->model_admin_employee->getDegrees();
		$data['channels'] = $this->model_admin_employee->getChannels();
		$data['marriages'] = $this->model_admin_employee->getMarriages();
		$data['relations'] = $this->model_admin_employee->getRelations();
		$data['politicses'] = $this->model_admin_employee->getPoliticses();
		$data['innames'] = $this->model_admin_employee->getInnames();

		$fields = array('fullname', 'telephone', 'sex', 'birth_date', 'nation', 'marriage', 'politics', 'address', 'card_img', 'card_no', 'card_name', 'card_address', 'card_expire', 'contact_name', 'contact_relation', 'contact_telephone', 'job_centre', 'job_department', 'job_post', 'job_office', 'job_number', 'job_work_date', 'job_estimate_date', 'job_regular_date', 'job_leave_date', 'job_status', 'job_confidentiality', 'edu_degree', 'edu_school', 'edu_speciality', 'edu_graduate_date', 'channel_from');

		foreach ($fields as $field) {
			if (isset($this->request->post[$field])) {
				$data[$field] = $this->request->post[$field];
			} elseif (!empty($employee_info)) {
				$data[$field] = $employee_info[$field];
			} else {
				$data[$field] = '';
			}
		}

		$data['department'] = $data['job_centre'] . '-' . $data['job_department'];

		if (isset($this->request->post['contracts'])) {
			$data['contracts'] = $this->request->post['contracts'];
		} elseif (!empty($employee_info)) {
			$data['contracts'] = $this->model_admin_employee->getContract($employee_info['employee_id']);
		} else {
			$data['contracts'] = array();
		}

		if (isset($this->request->post['insurances'])) {
			$data['insurances'] = $this->request->post['insurances'];
		} elseif (!empty($employee_info)) {
			$data['insurances'] = $this->model_admin_employee->getInsurance($employee_info['employee_id']);
		} else {
			$data['insurances'] = array();
		}
		
		$data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('employee/form.tpl', $data));
	}

	protected function validateForm() {
		if (!$this->user->hasPermission('modify', 'admin/employee')) {
			$this->error['warning'] = $this->language->get('error_permission');
		}

		if (empty($this->request->post['fullname'])) {
            $this->error['warning'] = '姓名不能为空！';
            return false;
        }

		if (empty($this->request->post['telephone'])) {
            $this->error['warning'] = '手机号不能为空！';
            return false;
        }

		if (!empty($this->request->post['department'])) {
			$department = explode('-', $this->request->post['department']);
			$this->request->post['job_centre'] = $department[0] ?? '';
			$this->request->post['job_department'] = $department[1] ?? '';
		} else {
			$this->error['warning'] = '所属部门不能为空！';
            return false;
		}

		if (empty($this->request->post['card_no']) || empty($this->request->post['card_name'])) {
            $this->error['warning'] = '身份证不能为空！';
            return false;
        }

		if (empty($this->request->post['contact_name']) || empty($this->request->post['contact_telephone'])) {
            $this->error['warning'] = '紧急联系人不能为空！';
            return false;
        }

		if (empty($this->request->post['birth_date'])) {
            $this->error['warning'] = '出生日期不能为空！';
            return false;
        }

		if (empty($this->request->post['job_work_date'])) {
            $this->error['warning'] = '入职时间不能为空！';
            return false;
        }

		if (empty($this->request->post['job_estimate_date'])) {
            $this->error['warning'] = '预计转正时间不能为空！';
            return false;
        }

		return !$this->error;
	}

	protected function validateDelete() {
		if (!$this->user->hasPermission('modify', 'admin/employee')) {
			$this->error['warning'] = $this->language->get('error_permission');
		}

		return !$this->error;
	}
}
